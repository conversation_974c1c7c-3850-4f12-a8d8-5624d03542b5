/*!
    \file  INS_Init.c
    \brief led spark with systick 
*/
#ifndef  __GOL_ALGORITHM_C__
#define  __GOL_ALGORITHM_C__
#include "appmain.h"
//#include "nav_includes.h"
//#include "frame_analysis.h"
#include "gdtypedefine.h"
#include "INS912AlgorithmEntry.h"
//#include "api_ch392.h"

#include "ins.h"
#include "uart.h"
#include "flash.h"
#include "bsp_gpio.h"
#include "bsp_exti.h"
#include "bsp_flash.h"


uint32_t g_bmp280_measTime;
uint8_t g_usb_ready;
char g_logFileName[256] = {0};
LogBufTypeDef g_LogBuf;
LogBufTypeDef* g_pLogBuf = &g_LogBuf;
int g_networkState = -1;
uint8_t fisrtTimeGNSSTimeSync = 1;
uint8_t fpga_syn = 0;//fpga同步
uint32_t fpga_syn_count =0;//fpga_syn硬触发计数
uint32_t fpga_loop_count =0;//fpga_syn主循环获取数据帧计数
int gcan0_rx_syn = 0;//can0 synchronization
int g_gpsWeek;							//GPS周内秒		周
double g_gpsSecond;						//GPS周内秒		秒
//unsigned short gfpgadata[200];
uint32_t g_CAN_Timeout_Start_flag = 0;
uint32_t g_CAN_Timeout_Cnt = 0;

uint8_t g_CAN_Count_Last = 0;

uint8_t g_KF_OutData_Rx_Flag;

//unsigned int gprotocol_send_baudrate = BAUD_RATE_921600;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
float calcGPRMC_TRA(char *pchar);
void wheel_is_running(void);
void NAV_Output(void);
void INS912_Output(navoutdata_t *pnavout);


char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
double get_16bit_D64(unsigned short *msgbuff)
{        
	#if 1
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    m16_uMemory.bd[2] = msgbuff[2];
    m16_uMemory.bd[3] = msgbuff[3];
	#else
    m16_uMemory.bd[0] = msgbuff[3];
    m16_uMemory.bd[1] = msgbuff[2];
    m16_uMemory.bd[2] = msgbuff[1];
    m16_uMemory.bd[3] = msgbuff[0];
	#endif
    return m16_uMemory.dv;
}
float get_16bit_D32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}

unsigned int  get_16bit_Int32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.iv;
}



void gd_eval_com_init();

// GD32F4xx平台适配函数
void Exti_Init(void)
{
    // GD32F4xx平台的外部中断初始化
    bsp_exti_init();
}

int norflash_init(void)
{
    // GD32F4xx平台的NOR Flash初始化
    // 这里可以初始化外部NOR Flash，如果没有外部Flash，可以使用内部Flash
    // 对于GD32F4xx，通常使用内部Flash
    fmc_unlock();
    return 0;
}

void Fatfs_Init(void)
{
    // GD32F4xx平台的FatFS初始化
    // 这里需要初始化SD卡和文件系统
    // 具体实现需要根据硬件配置
    printf("FatFS initialization for GD32F4xx\n");
}

void INS_Init(void)
{
	initializationdriversettings();
	bsp_gpio_init();
	bsp_tim_init();

	// 初始化EXMC以支持FPGA数据读取
	printf("Initializing EXMC for FPGA communication...\n");
	exmc_asynchronous_sram_init();
	printf("EXMC initialization completed\n");

	// 验证EXMC初始化
	printf("Testing EXMC access at 0x60000000...\n");
	volatile unsigned short test_val = *(unsigned short*)0x60000000;
	printf("Read test value: 0x%04X\n", test_val);

//	Uart_TxInit(UART_TXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
//	Uart_RxInit(UART_RXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
	//bsp_systick_init01(NULL);
//	mInitCH378Host();	//usb模块 沁恒
	
	//CAN1_STB_High();	//old
//	CAN1_STB_Low();		//new
//	CAN2_STB_High();
	
//	CAN1_Enable();
//	CAN2_Enable();
	
//	bsp_rtc_init(pRTC);
//	bsp_can_init(&hCAN0);
//	bsp_can_init(&hCAN1);
	//trng_configuration();
	//bsp_fwdgt_init();
	
	//TCPServer_Init();
	Exti_Init();
        Fatfs_Init();//SD卡
        norflash_init();//

	
//	g_bmp280_measTime = bmp280_init();
	
	memset(&hINSData,0,sizeof(hINSData));
	memset(&gdriverdatalist,0,sizeof(gdriverdatalist));

	
	
	
	//uart3 initialization... (改为UART3用于导航数据输出)
//	nvic_irq_enable(UART3_IRQn, 0, 0);  // UART3现在用于导航数据输出
//	nvic_irq_enable(UART4_IRQn, 0, 0);  // UART4现在用于RS422通信
	/* configure EVAL_COM3 */
    gd_eval_com_init();//串口初始化
    //gd_eval_com_init();

    /* enable USART receive interrupt */
//    usart_interrupt_enable(UART3, USART_INT_RBNE);  // UART3用于导航数据输出
//    usart_interrupt_enable(UART4, USART_INT_RBNE);  // UART4用于RS422通信

#ifdef DEVICE_ACC_TYPE_ADLX355
   ADXL355_UART7_Init();
#endif 


    /* enable USART0 transmit interrupt */
    //usart_interrupt_enable(UART4, USART_INT_TBE);
	
	/*comm_store_init();
	mDelaymS( 100 );
	if(RTC_BKP5 == CMD_BOOT) {
        RTC_BKP5 = 0;
        comm_send_end_frame(CMD_SET_FM_UPDATE);
    }*/
	
	g_LEDIndicatorState = LED_STATE_WHEEL_ERR_INIT_OK;
	delay_ms( 100 ); 
	
}

void GetChipID(void)
{
	hSetting.ChipID[0] = *(( volatile uint32_t * )0x1FFF7A10);
	hSetting.ChipID[1] = *(( volatile uint32_t * )0x1FFF7A14);
	hSetting.ChipID[2] = *(( volatile uint32_t * )0x1FFF7A18);
	hDefaultSetting.ChipID[0] = hSetting.ChipID[0];
	hDefaultSetting.ChipID[1] = hSetting.ChipID[1];
	hDefaultSetting.ChipID[2] = hSetting.ChipID[2];
}


int checkUSBReady(void)
{
//	uint8_t  status;
//	int i = 0;
//	for( i = 0; i < 10; i ++ )
//	{
//		status = CH378DiskReady( );		/* 初始化磁盘并测试磁盘是否就绪 */
//		if( status == ERR_SUCCESS ) 
//		{
////			CH378HardwareReset();
//			return 1;					/* 准备好 */
//		}
//		else if( status == ERR_DISK_DISCON ) 
//		{
//			return 0;					/* 检测到断开,重新检测并计时 */
//		}
//		if( CH378GetDiskStatus( ) >= DEF_DISK_MOUNTED && i >= 5 ) 
//		{
//			return 0;					/* 有的U盘总是返回未准备好,不过可以忽略,只要其建立连接MOUNTED且尝试5*50mS */
//		}
//	}
//	return 0;
}

//int queryDiskCapacity(void)
//{
//	UINT8 status;
//	status = CH378DiskCapacity(cap);
//}

void loggingLogFile(void* arg)
{
	LogBufTypeDef* pBuf = (LogBufTypeDef*)arg;
	
	unsigned char logfileName[128] = {0};
	memset(logfileName,0,128);
	generateCSVLogFileName((char*)logfileName);
	writeCSVLog(logfileName,pBuf);
}

/* retarget the C library printf function to the USART */
//int fputc(int ch, FILE *f)
//{
	//usart_data_transmit(USART0, (uint8_t)ch);
	//while(RESET == usart_flag_get(USART0, USART_FLAG_TC));
	//return ch;
//}

//读取FPGA数据
void StartNavigation(void)
{
	//通知ARM1进行卡尔曼滤波解算
	//ARM2_OUTPUT_ARM1_High();
	//delay_us(5);
	//ARM2_OUTPUT_ARM1_Low();

}

void StopNavigation(void)
{
	
}

void LEDIndicator(uint8_t state)
{
	switch(state)
	{
		case 0:
			break;
		case LED_STATE_WHEEL_OK_INIT_OK:	//1
			LED_STATE_ON();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_OK_INIT_ERR:	//2
			LED_STATE_OFF();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_ERR_INIT_OK:	//3
			LED_STATE_ON();
			LED_WHEEL_OFF();
			break;
		case LED_STATE_WHEEL_ERR_INIT_ERR:	//4
			LED_STATE_OFF();
			LED_WHEEL_OFF();
			break;
		default:
			break;
	}
}

/*!
    \brief      configure COM port
    \param[in]  COM: COM on the board
      \arg        EVAL_COM0: COM on the board
    \param[out] none
    \retval     none
*/
void gd_eval_com_init(void)
{
	UartIrqInit();
        SDUartIrqInit();
}


float calcGPRMC_TRA(char *pchar)
{
	char tmpchar;
	float multiplier = 1.0;
	int i, bfind = 0;
	float tra = 0.0;
	for (i = 0; i < 5; i++) {
		if (*(pchar + i) == '.') {
			bfind = 1;
			break;
		}
	}
	if (bfind) {
		tmpchar = *(pchar + i + 1);
		if (tmpchar < '0' || tmpchar > '9')	return	888.8;
		else tra = (tmpchar - '0') * 0.1;
		
		for (i-- ; i >= 0; i--) {
			tmpchar = *(pchar + i);
			if (tmpchar < '0' || tmpchar > '9')	break;
			else tra += (tmpchar - '0') * multiplier;
			//multiplier *= 10.0;
		}
		return tra;
	}
	return 999.9;
}

void sys_irq_stop(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_DISABLE);
}

void sys_irq_restart(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_ENABLE);
}
#endif


