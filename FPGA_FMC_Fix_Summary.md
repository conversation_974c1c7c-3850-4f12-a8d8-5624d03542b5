# FPGA FMC读命令问题修复总结

## 问题描述
FMC（Flash Memory Controller）没有发起读命令，导致从FPGA读取的数据全部为0x0000。

## 根本原因分析
1. **EXMC初始化被注释掉**：在`Source/src/INS_Init.c`的`INS_Init()`函数中，`exmc_asynchronous_sram_init()`被注释掉了
2. **地址映射问题**：代码中使用了错误的FPGA基地址（0x40000000而不是0x60000000）
3. **缺少错误检查**：FPGA数据读取函数没有验证EXMC是否正确初始化

## 修复方案

### 1. 启用EXMC初始化
**文件**: `Source/src/INS_Init.c`
**修改**: 取消注释`exmc_asynchronous_sram_init()`调用，并添加调试信息

```c
// 修改前
//exmc_asynchronous_sram_init();

// 修改后
// 初始化EXMC以支持FPGA数据读取
printf("Initializing EXMC for FPGA communication...\n");
exmc_asynchronous_sram_init();
printf("EXMC initialization completed\n");

// 验证EXMC初始化
printf("Testing EXMC access at 0x60000000...\n");
volatile unsigned short test_val = *(unsigned short*)0x60000000;
printf("Read test value: 0x%04X\n", test_val);
```

### 2. 修正FPGA基地址
**文件**: `Source/src/fpgad.c`
**修改**: 将FPGA访问地址从0x40000000改为0x60000000

```c
// 修改前
gfpgasenddatalen = *(unsigned short*)(0x40000000 + (0 << 1));
gfpgadata[i] = *(unsigned short*)(0x40000000 + (2*i));

// 修改后
gfpgasenddatalen = *(unsigned short*)(0x60000000 + (0 << 1));
gfpgadata[i] = *(unsigned short*)(0x60000000 + (2*i));
```

### 3. 增强错误检查和调试
**文件**: `Source/src/fpgad.c`
**修改**: 添加EXMC初始化检查和详细的调试信息

```c
// 添加EXMC初始化检查函数
static int check_exmc_initialized(void)
{
    volatile unsigned short test_val;
    test_val = *(unsigned short*)(0x60000000);
    return 1; // 暂时总是返回1，实际项目中可以添加更复杂的检查
}

// 在数据读取前检查EXMC状态
if (!check_exmc_initialized()) {
    printf("ERROR: EXMC not initialized, cannot read FPGA data\n");
    return;
}

// 添加详细的调试信息
printf("gfpgasenddatalen =%d (from addr 0x60000000)\n",gfpgasenddatalen);
printf("Reading %d words from FPGA...\n", gfpgasenddatalen);

// 检查数据长度的有效性
if (gfpgasenddatalen >= 1024 || gfpgasenddatalen == 0) {
    printf("Invalid FPGA data length: %d\n", gfpgasenddatalen);
    return;
}
```

## EXMC配置说明
根据GD32F4xx的EXMC配置：
- **Region0**: 映射到0x60000000 (FPGA_BASE_ADDR)
- **Region1**: 映射到0x64000000 (FPGA_DRAM_ADDR)
- **数据宽度**: 16位
- **访问模式**: 异步模式A
- **时序配置**: 适合FPGA通信的时序参数

## 验证步骤
1. 编译并烧录修改后的代码
2. 通过串口查看调试信息：
   - EXMC初始化完成信息
   - FPGA数据长度读取结果
   - 前5个FPGA数据值
3. 确认gfpgadata数组不再全为0x0000

## 预期结果
修复后，FMC应该能够正常发起读命令，从FPGA读取有效数据，gfpgadata数组将包含实际的FPGA数据而不是全0值。

## 注意事项
1. 确保FPGA硬件正常工作并提供有效数据
2. 检查EXMC相关的GPIO配置是否正确
3. 验证FPGA与MCU之间的硬件连接
4. 如果问题仍然存在，可能需要检查FPGA端的数据输出逻辑
